package com.eatapp.clementine.views

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.ImageView
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.viewpager2.widget.ViewPager2
import com.eatapp.clementine.R
import com.eatapp.clementine.adapter.EatPagerAdapter
import com.eatapp.clementine.internal.px
import com.eatapp.clementine.ui.reservation.ReservationDetailsFragment
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator

class ReservationTabLayout @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : TabLayout(context, attrs, defStyleAttr) {

    private val minSelectedTabWidth = 120.px

    private var viewPager: ViewPager2? = null
    private var adapter: EatPagerAdapter? = null
    private var mediator: TabLayoutMediator? = null
    private var isHandlingSharedTabs = false

    init {
        setupTabLayout()
    }

    private fun setupTabLayout() {
        addOnTabSelectedListener(object : OnTabSelectedListener {
            override fun onTabSelected(tab: Tab?) {
                updateTabs()
                handleTabSelection(tab)
            }

            override fun onTabUnselected(tab: Tab?) {}

            override fun onTabReselected(tab: Tab?) {
            }
        })
    }

    private fun handleTabSelection(tab: Tab?) {
        val position = tab?.position ?: return
        tab.select()

        // Handle shared fragment tabs (Details and Profile)
        if (position == 0 || position == 1) {
            isHandlingSharedTabs = true
            // Keep ViewPager on position 0 (the shared fragment) - no transition
            if (viewPager?.currentItem != 0) {
                viewPager?.setCurrentItem(0, false)
            }
            val fragment = adapter?.getItem(0)
            if (position == 0 && fragment is ReservationDetailsFragment) {
                fragment.scrollToDetails()
            }
            if (position == 1 && fragment is ReservationDetailsFragment) {
                fragment.scrollToProfile()
            }
            // Tab selection changes visually but fragment stays the same
            // The fragment will handle scrolling based on the tab selection
            return
        }

        // Handle normal tabs - adjust position because we have 2 tabs for 1 fragment
        isHandlingSharedTabs = false
        viewPager?.setCurrentItem(position, true)
    }

    fun setupWithViewPager(viewPager: ViewPager2, adapter: EatPagerAdapter) {
        this.viewPager = viewPager
        this.adapter = adapter

        for (i in 0 until adapter.itemCount) {
            val tab = newTab()
            setupCustomTab(tab, i)
            addTab(tab)
        }


//        mediator?.detach()
//
//        mediator = TabLayoutMediator(this, viewPager) { tab, position ->
//            setupCustomTab(tab, position)
//        }
//        mediator?.attach()

        // Override ViewPager behavior for shared tabs
        setupSharedTabBehavior(viewPager)
        viewPager.isUserInputEnabled = false

        post { updateTabs() }
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun setupSharedTabBehavior(viewPager: ViewPager2) {
        // Disable ViewPager page changes for the first two tabs (Details and Profile)
//        viewPager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
//
//            override fun onPageSelected(position: Int) {
//                super.onPageSelected(position)
//                if (selectedTabPosition == 1 && position == 0) {
//                    return
//                }
//                val tab = getTabAt(position)
//                handleTabSelection(tab)
//                updateTabs()
//            }
//        })
    }

    @SuppressLint("InflateParams")
    private fun setupCustomTab(tab: Tab, position: Int) {
        val customView = LayoutInflater.from(context).inflate(R.layout.tab_item_reservation, null)
        val tabIcon = customView.findViewById<ImageView>(R.id.tab_icon)
        val tabText = customView.findViewById<TextView>(R.id.tab_text)

        adapter?.let { adapter ->
            tabIcon.setImageResource(adapter.getPageIcon(position))
            tabText.text = adapter.getPageTitle(position)
        }

        tab.customView = customView
    }

    private fun updateTabs() {
        val tabCount = tabCount

        if (tabCount == 0) return

        post {
            if (width <= 0) return@post

            val (selectedTabWidth, unselectedTabWidth) = getTabWidth()

            for (i in 0 until tabCount) {
                val customView = getTabAt(i)?.customView
                val isSelected = i == selectedTabPosition

                customView?.apply {
                    val lp = layoutParams
                    lp.width = if (isSelected) selectedTabWidth else unselectedTabWidth
                    layoutParams = lp

                    val tabIcon = findViewById<ImageView>(R.id.tab_icon)
                    val tabText = findViewById<TextView>(R.id.tab_text)

                    if (isSelected) {
                        tabText?.visibility = VISIBLE
                        tabIcon?.setColorFilter(ContextCompat.getColor(context, R.color.colorPrimary))
                        tabText?.setTextColor(ContextCompat.getColor(context, R.color.colorPrimary))
                    } else {
                        tabText?.visibility = GONE
                        tabIcon?.setColorFilter(ContextCompat.getColor(context, R.color.colorDark50))
                    }
                }
            }
        }
    }

    private fun getTabWidth(): Pair<Int, Int> {
        val dynamicSize = width / tabCount
        val selectedTabWidth = maxOf(dynamicSize, minSelectedTabWidth).toInt()

        val remainingWidth = width - selectedTabWidth
        val unselectedTabCount = tabCount - 1
        val unselectedTabWidth = if (unselectedTabCount > 0) {
            maxOf(remainingWidth / unselectedTabCount, 48.px)
        } else {
            width
        }

        return Pair(selectedTabWidth, unselectedTabWidth)
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        mediator?.detach()
        mediator = null
        viewPager = null
        adapter = null
    }
}